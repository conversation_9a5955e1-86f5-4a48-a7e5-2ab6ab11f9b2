"use client"

import type React from "react"
import { useState } from "react"
import { Avatar, Button, Card, CardBody, CardHeader, Textarea, Input, Chip } from "@heroui/react"
import { Star, MessageCircle, Send, Image as ImageIcon, ThumbsUp, ThumbsDown, Edit2, Trash2 } from "lucide-react"
import type { Review, Reply } from "../service-review"
import ReviewImages from "../../../../components/ReviewImages/ReviewImages"
import { useAuth } from 'react-oidc-context'
import { toast } from 'react-toastify'


interface ReviewCardProps {
  review: Review
  onReply: (reviewId: string, reply: Omit<Reply, "id">) => void
  onLike?: (reviewId: string, type: 'like' | 'dislike') => void
  onReplyLike?: (reviewId: string, replyId: string, type: 'like' | 'dislike') => void
  onUpdateReply?: (reviewId: string, replyId: string, content: string) => void
  onDeleteReply?: (reviewId: string, replyId: string) => void
}

// Component to display detailed ratings
const DetailedRatings = ({ review }: { review: Review }) => {
  const ratings = [
    { label: "Service Quality", value: review.serviceRating },
    { label: "Work Quality", value: review.qualityRating },
    { label: "Value for Money", value: review.valueRating },
    { label: "Communication", value: review.communicationRating },
    { label: "Timeliness", value: review.timelinessRating },
  ].filter(rating => rating.value !== undefined && rating.value !== null)

  if (ratings.length === 0) return null

  return (
    <div className="space-y-2 border border-gray-200 rounded-lg p-3 bg-blue-50">
      <div className="flex items-center gap-2">
        <Star className="w-4 h-4 text-blue-600 fill-blue-600" />
        <h5 className="text-sm font-medium text-gray-700">Detailed Ratings</h5>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
        {ratings.map((rating) => (
          <div key={rating.label} className="flex items-center justify-between bg-white rounded-lg p-2 border border-gray-100">
            <span className="text-sm text-gray-600 font-medium">{rating.label}</span>
            <div className="flex items-center gap-2">
              <div className="flex">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className={`w-4 h-4 ${
                      star <= (rating.value || 0) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm font-semibold text-gray-700 bg-gray-100 px-2 py-1 rounded-full">
                {rating.value}/5
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export function ReviewCard({ review, onReply, onLike, onReplyLike, onUpdateReply, onDeleteReply }: ReviewCardProps) {
  const [showReplyForm, setShowReplyForm] = useState(false)
  const [replyContent, setReplyContent] = useState("")
  const [replyAuthor, setReplyAuthor] = useState("")
  const [expandedReplies, setExpandedReplies] = useState<Set<string>>(new Set())
  const [editingReplyId, setEditingReplyId] = useState<string | null>(null)
  const [editingContent, setEditingContent] = useState("")
  const auth = useAuth()

  const handleReplySubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate required fields
    if (!replyAuthor.trim()) {
      toast.error('Please enter your name')
      return
    }

    if (!replyContent.trim()) {
      toast.error('Please enter your reply')
      return
    }

    // Validate reply content length (backend expects 5-1000 characters)
    if (replyContent.trim().length < 5) {
      toast.error('Reply must be at least 5 characters long')
      return
    }

    if (replyContent.trim().length > 1000) {
      toast.error('Reply must be no more than 1000 characters long')
      return
    }

    if (!auth?.isAuthenticated) {
      toast.error('Please log in to reply to reviews')
      return
    }

    try {
      await onReply(review.id, {
        authorName: replyAuthor.trim(),
        content: replyContent.trim(),
        date: new Date().toISOString().split("T")[0],
        isBusinessOwner: false,
        replies: [],
        likes: 0,
        dislikes: 0,
        userReaction: null,
      })

      setReplyContent("")
      setReplyAuthor("")
      setShowReplyForm(false)
      toast.success('Reply submitted successfully!')
    } catch (error: any) {
      console.error('Error submitting reply:', error)
      toast.error(error.message || 'Failed to submit reply. Please try again.')
    }
  }

  const handleEditReply = (replyId: string, currentContent: string) => {
    setEditingReplyId(replyId)
    setEditingContent(currentContent)
  }

  const handleUpdateReplySubmit = (replyId: string) => {
    if (!editingContent.trim() || !onUpdateReply) return

    onUpdateReply(review.id, replyId, editingContent.trim())
    setEditingReplyId(null)
    setEditingContent("")
  }

  const handleCancelEdit = () => {
    setEditingReplyId(null)
    setEditingContent("")
  }

  const handleDeleteReplyClick = (replyId: string) => {
    if (!onDeleteReply) return

    if (window.confirm('Are you sure you want to delete this reply?')) {
      onDeleteReply(review.id, replyId)
    }
  }

  // Handle like/dislike for review
  const handleReviewLike = (type: 'like' | 'dislike') => {
    if (!auth?.isAuthenticated) {
      toast.error('Please log in to react to reviews')
      return
    }
    if (onLike) {
      onLike(review.id, type)
    }
  }

  // Handle like/dislike for reply
  const handleReplyReaction = (replyId: string, type: 'like' | 'dislike') => {
    if (!auth?.isAuthenticated) {
      toast.error('Please log in to react to replies')
      return
    }
    if (onReplyLike) {
      onReplyLike(review.id, replyId, type)
    }
  }

  // Toggle reply expansion
  const toggleReplyExpansion = (replyId: string) => {
    const newExpanded = new Set(expandedReplies)
    if (newExpanded.has(replyId)) {
      newExpanded.delete(replyId)
    } else {
      newExpanded.add(replyId)
    }
    setExpandedReplies(newExpanded)
  }

  return (
    <Card className="shadow-sm border border-gray-200 hover:border-gray-300 transition-colors">
      <CardHeader className="pb-3 border-b border-gray-100">
        <div className="flex items-start gap-4">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold">{review.customerName || 'Anonymous User'}</h3>
              <span className="text-sm text-gray-500">{review.date}</span>
              {review.isVerified && (
                <Chip size="sm" color="success" variant="flat" startContent="✓">
                  Verified
                </Chip>
              )}
            </div>
            {/* Show reviewer email if available */}
            {(review.reviewerInfo?.email || review.userEmail || review.email) && (
              <div className="mb-2">
                <span className="text-sm text-gray-600">
                  {review.reviewerInfo?.email || review.userEmail || review.email}
                </span>
              </div>
            )}
            <div className="flex items-center gap-2 mb-2">
              <div className="flex">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className={`w-4 h-4 ${
                      star <= review.rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm font-medium">{review.rating}/5</span>
            </div>
            <h4 className="font-medium mb-2">{review.title}</h4>
          </div>
        </div>
      </CardHeader>
      <CardBody className="space-y-2 py-3">
        <div className="border border-gray-200 rounded-lg p-3 bg-gray-50">
          <p className="text-gray-700 leading-snug font-bold">{review.content}</p>
        </div>

        {/* Detailed Ratings */}
        <DetailedRatings review={review} />

        {/* Review Images */}
        {review.imageNames && review.imageNames.length > 0 && (
          <div className="space-y-2 border border-gray-200 rounded-lg p-3 bg-gray-50">
            <div className="flex items-center gap-2">
              <ImageIcon className="w-4 h-4 text-blue-600" />
              <h5 className="text-sm font-medium text-gray-700">Review Photos</h5>
              <Chip size="sm" variant="flat" color="primary" className="text-xs">
                {review.imageNames.length} {review.imageNames.length === 1 ? 'photo' : 'photos'}
              </Chip>
            </div>
            <div className="bg-white rounded-lg p-0.5 border border-gray-100">
              <ReviewImages
                imageNames={review.imageNames}
                imageUrls={review.imageUrls}
                maxDisplay={6}
                size="md"
                layout="grid-3"
                enablePreview={true}
                showCount={true}
                folderName="review-images"
                className=""
              />
            </div>
          </div>
        )}

        {review.replies.length > 0 && (
          <div className="space-y-3 border border-gray-200 rounded-lg p-3 bg-gray-50">
            <div className="flex items-center gap-2 mb-3">
              <MessageCircle className="w-4 h-4 text-green-600" />
              <h5 className="text-sm font-medium text-gray-700">Replies ({review.replies.length})</h5>
            </div>
            <div className="space-y-4">
              {review.replies.map((reply) => (
                <div key={reply.id} className="flex gap-3 pl-4 border-l-2 border-gray-200">
                  <Avatar
                    src={reply.authorAvatar || "/placeholder.svg"}
                    name={reply.authorName}
                    size="sm"
                  />
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-sm">{reply.authorName}</span>
                        {reply.isBusinessOwner && (
                          <Chip size="sm" color="primary" variant="flat" className="text-xs">
                            Business
                          </Chip>
                        )}
                        <span className="text-xs text-gray-500">{reply.date}</span>
                      </div>

                      {/* Edit/Delete buttons for authenticated users */}
                      {auth?.isAuthenticated && (onUpdateReply || onDeleteReply) && (
                        <div className="flex items-center gap-1">
                          {onUpdateReply && (
                            <Button
                              variant="light"
                              size="sm"
                              onPress={() => handleEditReply(reply.id, reply.content)}
                              startContent={<Edit2 className="w-3 h-3" />}
                              className="text-xs text-gray-400 hover:text-blue-500 min-w-0 px-2"
                            >
                              Edit
                            </Button>
                          )}
                          {onDeleteReply && (
                            <Button
                              variant="light"
                              size="sm"
                              onPress={() => handleDeleteReplyClick(reply.id)}
                              startContent={<Trash2 className="w-3 h-3" />}
                              className="text-xs text-gray-400 hover:text-red-500 min-w-0 px-2"
                            >
                              Delete
                            </Button>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Reply content - editable if in edit mode */}
                    {editingReplyId === reply.id ? (
                      <div className="mb-2">
                        <Textarea
                          value={editingContent}
                          onValueChange={setEditingContent}
                          minRows={2}
                          variant="bordered"
                          className="mb-2"
                        />
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            color="primary"
                            onPress={() => handleUpdateReplySubmit(reply.id)}
                          >
                            Save
                          </Button>
                          <Button
                            size="sm"
                            variant="light"
                            onPress={handleCancelEdit}
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <p className="text-sm text-gray-600 mb-2">{reply.content}</p>
                    )}

                    {/* Reply Actions */}
                    <div className="flex items-center gap-2">
                      <Button
                        variant="light"
                        size="sm"
                        onPress={() => handleReplyReaction(reply.id, 'like')}
                        startContent={
                          <ThumbsUp
                            className={`w-3 h-3 ${reply.userReaction === 'like' ? 'fill-blue-500 text-blue-500' : 'text-gray-400'}`}
                          />
                        }
                        className={`text-xs ${reply.userReaction === 'like' ? 'text-blue-500' : 'text-gray-400'}`}
                      >
                        {reply.likes}
                      </Button>
                      <Button
                        variant="light"
                        size="sm"
                        onPress={() => handleReplyReaction(reply.id, 'dislike')}
                        startContent={
                          <ThumbsDown
                            className={`w-3 h-3 ${reply.userReaction === 'dislike' ? 'fill-red-500 text-red-500' : 'text-gray-400'}`}
                          />
                        }
                        className={`text-xs ${reply.userReaction === 'dislike' ? 'text-red-500' : 'text-gray-400'}`}
                      >
                        {reply.dislikes}
                      </Button>

                      {/* Nested Reply Button */}
                      <Button
                        variant="light"
                        size="sm"
                        onPress={() => toggleReplyExpansion(reply.id)}
                        startContent={<MessageCircle className="w-3 h-3" />}
                        className="text-xs text-gray-400"
                      >
                        Reply
                      </Button>
                    </div>

                    {/* Nested Replies */}
                    {reply.replies && reply.replies.length > 0 && (
                      <div className="mt-3 space-y-2">
                        {reply.replies.slice(0, expandedReplies.has(reply.id) ? reply.replies.length : 2).map((nestedReply) => (
                          <div key={nestedReply.id} className="flex gap-2 pl-2 border-l border-gray-100">
                            <Avatar
                              src={nestedReply.authorAvatar || "/placeholder.svg"}
                              name={nestedReply.authorName}
                              size="sm"
                              className="w-6 h-6"
                            />
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="font-medium text-xs">{nestedReply.authorName}</span>
                                {nestedReply.isBusinessOwner && (
                                  <Chip size="sm" color="primary" variant="flat" className="text-[10px] px-1 py-0">
                                    Business
                                  </Chip>
                                )}
                                <span className="text-[10px] text-gray-500">{nestedReply.date}</span>
                              </div>
                              <p className="text-xs text-gray-600">{nestedReply.content}</p>
                            </div>
                          </div>
                        ))}

                        {reply.replies.length > 2 && !expandedReplies.has(reply.id) && (
                          <Button
                            variant="light"
                            size="sm"
                            onPress={() => toggleReplyExpansion(reply.id)}
                            className="text-xs text-blue-500"
                          >
                            Show {reply.replies.length - 2} more replies
                          </Button>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
          <div className="flex items-center gap-4">
            {/* Like/Dislike Buttons */}
            <div className="flex items-center gap-2">
              <Button
                variant="light"
                size="sm"
                onPress={() => handleReviewLike('like')}
                startContent={
                  <ThumbsUp
                    className={`w-4 h-4 ${review.userReaction === 'like' ? 'fill-blue-500 text-blue-500' : 'text-gray-500'}`}
                  />
                }
                className={review.userReaction === 'like' ? 'text-blue-500' : 'text-gray-500'}
              >
                {review.likes}
              </Button>
              <Button
                variant="light"
                size="sm"
                onPress={() => handleReviewLike('dislike')}
                startContent={
                  <ThumbsDown
                    className={`w-4 h-4 ${review.userReaction === 'dislike' ? 'fill-red-500 text-red-500' : 'text-gray-500'}`}
                  />
                }
                className={review.userReaction === 'dislike' ? 'text-red-500' : 'text-gray-500'}
              >
                {review.dislikes}
              </Button>
            </div>

            {/* Reply Button */}
            <Button
              variant="light"
              size="sm"
              onPress={() => setShowReplyForm(!showReplyForm)}
              startContent={<MessageCircle className="w-4 h-4" />}
            >
              Reply ({review.replies.length})
            </Button>
          </div>

          {/* Review Metadata */}
          <div className="flex items-center gap-2">
            {review.helpfulCount && review.helpfulCount > 0 && (
              <Chip size="sm" color="primary" variant="flat">
                {review.helpfulCount} found helpful
              </Chip>
            )}
          </div>
        </div>

        {showReplyForm && (
          <form onSubmit={handleReplySubmit} className="space-y-3 pt-2 border-t">
            <Input
              placeholder="Your name"
              value={replyAuthor}
              onValueChange={setReplyAuthor}
              isRequired
              variant="bordered"
            />
            <Textarea
              placeholder="Write your reply..."
              value={replyContent}
              onValueChange={setReplyContent}
              minRows={3}
              maxRows={6}
              isRequired
              variant="bordered"
              description={`${replyContent.length}/1000 characters`}
              isInvalid={replyContent.length > 1000 || (replyContent.length > 0 && replyContent.length < 5)}
              errorMessage={
                replyContent.length > 1000
                  ? "Reply must be no more than 1000 characters"
                  : replyContent.length > 0 && replyContent.length < 5
                    ? "Reply must be at least 5 characters"
                    : ""
              }
            />
            <div className="flex gap-2">
              <Button
                type="submit"
                size="sm"
                color="primary"
                startContent={<Send className="w-4 h-4" />}
              >
                Post Reply
              </Button>
              <Button
                type="button"
                variant="light"
                size="sm"
                onPress={() => setShowReplyForm(false)}
              >
                Cancel
              </Button>
            </div>
          </form>
        )}
      </CardBody>
    </Card>
  )
}
