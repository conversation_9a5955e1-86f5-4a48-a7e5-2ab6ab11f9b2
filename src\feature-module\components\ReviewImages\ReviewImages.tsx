import React from 'react';
import { getImageUrlsFromNames, getImageUrlWithFallback, debugS3Configuration } from '../../frontend/Customer/aws/s3FileUpload';

interface ReviewImagesProps {
  imageNames?: string[];
  imageUrls?: string[];
  maxDisplay?: number;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showCount?: boolean;
  folderName?: string;
  layout?: 'row' | 'column' | 'grid-4';
  enablePreview?: boolean;
  onImageClick?: (images: string[], index: number) => void;
}

const ReviewImages: React.FC<ReviewImagesProps> = ({
  imageNames = [],
  imageUrls = [],
  maxDisplay = 3,
  size = 'md',
  className = '',
  showCount = true,
  folderName = 'review-images',
  layout = 'row',
  enablePreview = false,
  onImageClick
}) => {
  // State for image preview
  const [previewImage, setPreviewImage] = React.useState<string | null>(null);

  // State to track failed images
  const [failedImages, setFailedImages] = React.useState<Set<string>>(new Set());

  // State to track loading images
  const [loadingImages, setLoadingImages] = React.useState<Set<string>>(new Set());

  // Determine which images to display - prioritize imageNames over imageUrls
  const displayImages = React.useMemo(() => {
    if (imageNames.length > 0) {
      // Convert image names to URLs using the synchronous method
      const urls = getImageUrlsFromNames(imageNames, folderName);
      const validUrls = urls.filter(url => url && typeof url === 'string' && url.trim() !== '');

      console.log('ReviewImages: Converting image names to URLs:', {
        imageNames,
        folderName,
        generatedUrls: urls,
        validUrls: validUrls.length
      });
      return validUrls;
    } else if (imageUrls.length > 0) {
      // Use provided URLs as fallback
      const validUrls = imageUrls.filter(url => url && typeof url === 'string' && url.trim() !== '');
      console.log('ReviewImages: Using provided URLs:', {
        originalUrls: imageUrls,
        validUrls: validUrls.length
      });
      return validUrls;
    }
    console.log('ReviewImages: No images to display');
    return [];
  }, [imageNames, imageUrls, folderName]);

  // Get size classes with responsive design
  const sizeClasses = {
    sm: 'w-12 h-12 sm:w-14 sm:h-14',
    md: 'w-16 h-16 sm:w-20 sm:h-20',
    lg: 'w-24 h-24 sm:w-32 sm:h-32 md:w-36 md:h-36',
    xl: 'w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48'
  };

  const imageSize = sizeClasses[size];
  const totalImages = imageNames.length > 0 ? imageNames.length : imageUrls.length;

  // Don't render if no images
  if (displayImages.length === 0) {
    console.log('ReviewImages: No images to display, returning null');
    return null;
  }

  // Determine layout classes with minimal spacing
  const layoutClasses = layout === 'column'
    ? 'flex flex-col gap-0.5'
    : layout === 'grid-4'
    ? 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-0.5'
    : 'flex flex-wrap gap-0.5';

  // Handle image click for preview
  const handleImageClick = (imageUrl: string, index: number) => {
    if (onImageClick) {
      // Use external callback if provided
      onImageClick(displayImages, index);
    } else if (enablePreview) {
      // Use internal preview if no external callback
      setPreviewImage(imageUrl);
    }
  };

  // Close preview
  const closePreview = () => {
    setPreviewImage(null);
  };

  return (
    <>
      <div className={`${layoutClasses} ${className}`}>
        {/* Display images up to maxDisplay limit */}
        {displayImages.slice(0, maxDisplay).map((imageUrl, idx) => (
          <div key={idx} className="relative group">
            {/* Loading indicator */}
            {loadingImages.has(imageUrl) && (
              <div className={`${imageSize} absolute inset-0 bg-gray-100 rounded-lg border border-gray-200 flex items-center justify-center`}>
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
              </div>
            )}

            <img
              src={imageUrl}
              alt={`Review image ${idx + 1}`}
              className={`${imageSize} object-cover rounded-lg border border-gray-200 shadow-sm hover:shadow-lg transition-all duration-300 ${(enablePreview || onImageClick) ? 'cursor-pointer hover:opacity-90 hover:scale-105' : ''} ${loadingImages.has(imageUrl) ? 'opacity-0' : 'opacity-100'} ${failedImages.has(imageUrl) ? 'opacity-50' : ''}`}
              onClick={() => handleImageClick(imageUrl, idx)}
              onLoadStart={() => {
                setLoadingImages(prev => new Set(prev).add(imageUrl));
                setFailedImages(prev => {
                  const newSet = new Set(prev);
                  newSet.delete(imageUrl);
                  return newSet;
                });
              }}
              onLoad={() => {
                setLoadingImages(prev => {
                  const newSet = new Set(prev);
                  newSet.delete(imageUrl);
                  return newSet;
                });
                setFailedImages(prev => {
                  const newSet = new Set(prev);
                  newSet.delete(imageUrl);
                  return newSet;
                });
              }}
              onError={(e) => {
                // Enhanced error handling for broken images
                const target = e.target as HTMLImageElement;
                const errorDetails = {
                  imageUrl,
                  imageIndex: idx,
                  imageName: imageNames[idx] || 'N/A',
                  folderName,
                  originalSrc: target.src,
                  timestamp: new Date().toISOString()
                };

                console.error('ReviewImages: Failed to load image:', errorDetails);

                // Track failed image and clear loading state
                setFailedImages(prev => new Set(prev).add(imageUrl));
                setLoadingImages(prev => {
                  const newSet = new Set(prev);
                  newSet.delete(imageUrl);
                  return newSet;
                });

                // Run configuration debug on first error
                if (idx === 0) {
                  console.log('Running configuration debug due to image load failure...');
                  debugS3Configuration();
                }

                // Prevent infinite error loops
                if (target.src.includes('data:image/svg+xml') ||
                    target.src.includes('placeholder') ||
                    target.src.includes('fallback-attempted') ||
                    target.src.includes('folder-fallback-attempted') ||
                    target.src.includes('via.placeholder.com')) {
                  return;
                }

                // Try S3 direct URL fallback if we have an image name and current URL is CDN
                const imageName = imageNames[idx];
                if (imageName && target.src.includes('cdn.staging.gigmosaic.ca')) {
                  const fallbackUrl = getImageUrlWithFallback(imageName, folderName);
                  if (fallbackUrl && fallbackUrl !== imageUrl && fallbackUrl.includes('amazonaws.com')) {
                    console.log('ReviewImages: Trying S3 direct URL fallback:', {
                      originalUrl: imageUrl,
                      fallbackUrl,
                      imageName,
                      reason: 'CDN_failure_detected'
                    });
                    target.src = fallbackUrl + '?fallback-attempted=true';
                    return;
                  }
                }

                // Try alternative folder names if the current one fails
                if (imageName && target.src.includes('amazonaws.com') && !target.src.includes('fallback-attempted')) {
                  const alternativeFolders = ['review-images', 'reviews'];
                  const currentFolder = folderName;
                  const alternativeFolder = alternativeFolders.find(folder => folder !== currentFolder);

                  if (alternativeFolder) {
                    const alternativeUrl = getImageUrlWithFallback(imageName, alternativeFolder);
                    if (alternativeUrl && alternativeUrl !== imageUrl) {
                      console.log('ReviewImages: Trying alternative folder:', {
                        originalUrl: imageUrl,
                        alternativeUrl,
                        imageName,
                        currentFolder,
                        alternativeFolder,
                        reason: 'folder_mismatch_fallback'
                      });
                      target.src = alternativeUrl + '?folder-fallback-attempted=true';
                      return;
                    }
                  }
                }

                // Create a simple SVG placeholder as final fallback
                const svgPlaceholder = `data:image/svg+xml;base64,${btoa(`
                  <svg width="150" height="150" xmlns="http://www.w3.org/2000/svg">
                    <rect width="150" height="150" fill="#f3f4f6" stroke="#e5e7eb" stroke-width="1"/>
                    <circle cx="75" cy="60" r="15" fill="#d1d5db"/>
                    <rect x="50" y="85" width="50" height="30" rx="4" fill="#d1d5db"/>
                    <text x="75" y="130" font-family="Arial, sans-serif" font-size="12" fill="#9ca3af" text-anchor="middle">Image</text>
                    <text x="75" y="145" font-family="Arial, sans-serif" font-size="12" fill="#9ca3af" text-anchor="middle">Not Found</text>
                  </svg>
                `)}`;

                console.log('Using SVG placeholder as final fallback');
                target.src = svgPlaceholder;
              }}
            />

            {/* Click to view overlay */}
            {(enablePreview || onImageClick) && (
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 rounded-lg flex items-center justify-center">
                <div className="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-xs font-medium bg-black bg-opacity-50 px-2 py-1 rounded">
                  Click to view
                </div>
              </div>
            )}


          </div>
        ))}

        {/* Show count indicator if there are more images */}
        {showCount && totalImages > maxDisplay && (
          <div
            className={`${imageSize} bg-gray-100 hover:bg-gray-200 rounded-lg border border-gray-200 flex flex-col items-center justify-center text-xs text-gray-600 font-medium transition-all duration-300 cursor-pointer hover:shadow-md group`}
            onClick={() => onImageClick && onImageClick(displayImages, maxDisplay)}
            title={`View all ${totalImages} images`}
          >
            <div className="text-lg font-bold text-gray-700 group-hover:text-gray-900">
              +{totalImages - maxDisplay}
            </div>
            <div className="text-[10px] text-gray-500 group-hover:text-gray-700">
              more
            </div>
          </div>
        )}
      </div>

      {/* Image Preview Modal */}
      {enablePreview && previewImage && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
          onClick={closePreview}
        >
          <div className="relative w-full h-full max-w-6xl max-h-6xl flex items-center justify-center">
            <img
              src={previewImage}
              alt="Preview"
              className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
              onClick={(e) => e.stopPropagation()}
            />
            <button
              onClick={closePreview}
              className="absolute top-4 right-4 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full p-3 transition-all duration-200 hover:scale-110"
              title="Close preview"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default ReviewImages;

// Hook for managing review images
export const useReviewImages = (imageNames: string[], imageUrls: string[], folderName = 'review-images') => {
  const displayUrls = React.useMemo(() => {
    if (imageNames.length > 0) {
      return getImageUrlsFromNames(imageNames, folderName);
    }
    return imageUrls;
  }, [imageNames, imageUrls, folderName]);

  const hasImages = imageNames.length > 0 || imageUrls.length > 0;
  const totalCount = imageNames.length > 0 ? imageNames.length : imageUrls.length;

  return {
    displayUrls,
    hasImages,
    totalCount,
    imageNames,
    imageUrls
  };
};
