"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>gin<PERSON> } from "@heroui/react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { ReviewCard } from "./review-card"
import type { Review, Reply } from "../service-review"

interface ReviewListProps {
  reviews: Review[]
  onReply: (reviewId: string, reply: Omit<Reply, "id">) => void
  onLike?: (reviewId: string, type: 'like' | 'dislike') => void
  onReplyLike?: (reviewId: string, replyId: string, type: 'like' | 'dislike') => void
  onUpdateReply?: (reviewId: string, replyId: string, content: string) => void
  onDeleteReply?: (reviewId: string, replyId: string) => void
}

const REVIEWS_PER_PAGE = 2

export function ReviewList({ reviews, onReply, onLike, onReplyLike, onUpdateReply, onDeleteReply }: ReviewListProps) {
  const [currentPage, setCurrentPage] = useState(1)

  if (reviews.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">No reviews yet. Be the first to share your experience!</p>
      </div>
    )
  }

  // Calculate pagination
  const totalPages = Math.ceil(reviews.length / REVIEWS_PER_PAGE)
  const startIndex = (currentPage - 1) * REVIEWS_PER_PAGE
  const endIndex = startIndex + REVIEWS_PER_PAGE
  const currentReviews = reviews.slice(startIndex, endIndex)

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    // Scroll to top of reviews section
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  return (
    <div className="space-y-6">
      {/* Reviews Display */}
      <div className="space-y-3">
        {currentReviews.map((review) => (
          <ReviewCard
            key={review.id}
            review={review}
            onReply={onReply}
            onLike={onLike}
            onReplyLike={onReplyLike}
            onUpdateReply={onUpdateReply}
            onDeleteReply={onDeleteReply}
          />
        ))}
      </div>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex flex-col items-center gap-4 pt-6 border-t border-gray-200">
          {/* Pagination Info */}
          <div className="text-sm text-gray-600">
            Showing {startIndex + 1}-{Math.min(endIndex, reviews.length)} of {reviews.length} reviews
          </div>

          {/* Pagination Component */}
          <div className="flex items-center gap-2">
            <Button
              variant="light"
              size="sm"
              onPress={() => handlePageChange(currentPage - 1)}
              isDisabled={currentPage === 1}
              startContent={<ChevronLeft className="w-4 h-4" />}
            >
              Previous
            </Button>

            <Pagination
              total={totalPages}
              page={currentPage}
              onChange={handlePageChange}
              size="sm"
              showControls={false}
              classNames={{
                wrapper: "gap-1",
                item: "w-8 h-8 text-sm",
                cursor: "bg-primary text-white font-medium"
              }}
            />

            <Button
              variant="light"
              size="sm"
              onPress={() => handlePageChange(currentPage + 1)}
              isDisabled={currentPage === totalPages}
              endContent={<ChevronRight className="w-4 h-4" />}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
